.address-wrapper {
    padding: 16rpx 24rpx !important;
    background: #fff;
}

.address-title {
    display: flex;
    align-items: center;
    gap: 8rpx;

    color: #666;
    font-size: 20rpx;
}

.default-tag {
    border-radius: 8rpx;
    background: rgba(#FFE8E8, 0.59);
    padding: 4rpx 12rpx;
    color: #F00;
    font-size: 16rpx;
}

.address-wrapper-left {
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.address-detail {
    color: #333;
    font-size: 24rpx;
    font-weight: 500;
}

.address-wrapper-bottom {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.address-name {
    color: #333;
    font-size: 20rpx;
    font-weight: 500;
}

.address-phone {
    color: #333;
    font-size: 20rpx;
    font-weight: 500;
}

// 店铺
.shop-name {
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
}